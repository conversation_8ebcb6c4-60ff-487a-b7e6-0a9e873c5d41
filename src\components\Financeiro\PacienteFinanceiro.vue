<template>
  <div>
    
    <!-- Filtros -->
    <div class="card">
      <div class="card-body">
        <div class="row g-3">
          <!-- Primeira linha -->
          <div class="col-md-2">
            <label class="form-label">Data Início</label>
            <input type="date" class="form-control" v-model="filters.data_inicio" @change="applyFilters">
          </div>
          <div class="col-md-2">
            <label class="form-label">Data Fim</label>
            <input type="date" class="form-control" v-model="filters.data_fim" @change="applyFilters">
          </div>
          <div class="col-md-8 d-flex align-items-end gap-2 justify-content-end">
            <button class="btn btn-outline-primary elegant-action-btn-small" @click="$emit('create')">
              <i class="fas fa-plus me-1"></i>
              Nova Fatura
            </button>
            <button class="btn btn-primary elegant-action-btn-small" @click="$emit('create-orcamento')">
              <i class="fas fa-calculator me-1"></i>
              Novo Orçamento
            </button>
          </div>
        </div>
        <div class="row g-3 mt-1">
          <!-- Segunda linha -->
          <div class="col-md-3">
            <label class="form-label">Status</label>
            <div class="status-toggles">
              <button
                v-for="status in statusOptions"
                :key="status.value"
                class="btn status-toggle"
                :class="getStatusToggleClass(status.value)"
                @click="toggleStatus(status.value)"
                :title="status.label"
              >
                <i :class="status.icon" class="me-1"></i>
                {{ status.label }}
              </button>
            </div>
          </div>
          <div class="col-md-7">
            <label class="form-label">Descrição</label>
            <input type="text" class="form-control" v-model="filters.descricao" @input="applyFilters" placeholder="Buscar por descrição...">
          </div>
          <div class="col-md-2 d-flex align-items-end gap-2">
            <button class="btn btn-primary" @click="applyFilters" title="Buscar">
              <i class="fas fa-search me-1"></i>
              Buscar
            </button>
            <button class="btn btn-outline-secondary" @click="clearFilters" title="Limpar filtros">
              <i class="fas fa-broom me-1"></i>
              Limpar
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Resumo Financeiro -->
    <div class="container-fluid py-3">
      <div class="row">
        <div class="col-md-3">
          <div class="stats-card">
            <div class="stats-title">
              <i class="fas fa-check-circle me-1"></i>
              Total Pago
            </div>
            <div class="stats-value text-success">{{ formatCurrency(totalPago) }}</div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="stats-card">
            <div class="stats-title">
              <i class="fas fa-clock me-1"></i>
              Total Pendente
            </div>
            <div class="stats-value text-warning">{{ formatCurrency(totalPendente) }}</div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="stats-card">
            <div class="stats-title">
              <i class="fas fa-exclamation-triangle me-1"></i>
              Total Vencido
            </div>
            <div class="stats-value text-danger">{{ formatCurrency(totalVencido) }}</div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="stats-card">
            <div class="stats-title">
              <i class="fas fa-calculator me-1"></i>
              Total Geral
            </div>
            <div class="stats-value text-info">{{ formatCurrency(totalGeral) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Lista de Faturas -->
    <div class="card flex-fill">
      <div class="card-body px-0 pt-0 pb-2">
        <div class="table-responsive p-0">
          <table class="table align-items-center mb-0">
                <thead>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Descrição
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Valor
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Emissão
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Vencimento
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Status
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="loading">
                    <td colspan="6" class="text-center py-4">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="filteredFaturas.length === 0">
                    <td colspan="6" class="text-center py-4 text-muted">
                      {{ faturas.length === 0 ? 'Nenhuma fatura encontrada para este paciente' : 'Nenhuma fatura corresponde aos filtros aplicados' }}
                    </td>
                  </tr>
                  <tr v-else v-for="fatura in filteredFaturas" :key="fatura.id">
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm">{{ fatura.descricao }}</h6>
                          <p class="text-xs text-secondary mb-0" v-if="fatura.observacoes">
                            {{ fatura.observacoes }}
                          </p>
                          <div v-if="fatura.parcelas_total > 1" class="text-xs text-info">
                            Parcela {{ fatura.parcela_numero }}/{{ fatura.parcelas_total }}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="font-weight-bold">{{ formatCurrency(fatura.valor_final) }}</span>
                      <div v-if="fatura.valor_nominal !== fatura.valor_final" class="text-xs text-secondary">
                        Original: {{ formatCurrency(fatura.valor_nominal) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_emissao) }}
                      </span>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_vencimento) }}
                      </span>
                      <div v-if="isOverdue(fatura)" class="text-xs text-danger">
                        Vencida há {{ getDaysOverdue(fatura) }} dias
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ getStatusText(fatura.status) }}
                      </span>
                      <div v-if="fatura.data_pagamento" class="text-xs text-secondary mt-1">
                        Pago em {{ formatDate(fatura.data_pagamento) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <div class="dropdown">
                        <button class="btn btn-link text-secondary mb-0" data-bs-toggle="dropdown">
                          <i class="fas fa-ellipsis-v"></i>
                        </button>
                        <ul class="dropdown-menu">
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('edit', fatura)">
                              <i class="fas fa-edit me-2"></i>
                              Editar
                            </a>
                          </li>
                          <li v-if="fatura.status === 'pendente'">
                            <a class="dropdown-item" href="#" @click.prevent="markAsPaid(fatura)">
                              <i class="fas fa-check me-2"></i>
                              Marcar como Pago
                            </a>
                          </li>
                          <li>
                            <a class="dropdown-item" href="#" @click.prevent="$emit('generate-receipt', fatura)">
                              <i class="fas fa-file-pdf me-2"></i>
                              Gerar Recibo
                            </a>
                          </li>
                          <li>
                            <hr class="dropdown-divider">
                          </li>
                          <li>
                            <a class="dropdown-item text-danger" href="#" @click.prevent="$emit('delete', fatura.id)">
                              <i class="fas fa-trash me-2"></i>
                              Cancelar
                            </a>
                          </li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
</template>

<script>
import { financeiroService } from '@/services/financeiroService';

export default {
  name: 'PacienteFinanceiro',
  props: {
    paciente: {
      type: Object,
      required: true
    },
    faturas: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      filters: {
        statusList: [], // Array de status ativos
        descricao: '',
        data_inicio: '',
        data_fim: ''
      },
      statusOptions: [
        { value: 'pendente', label: 'Pendente', icon: 'fas fa-clock' },
        { value: 'pago', label: 'Pago', icon: 'fas fa-check-circle' },
        { value: 'vencido', label: 'Vencido', icon: 'fas fa-exclamation-triangle' },
        { value: 'cancelado', label: 'Cancelado', icon: 'fas fa-times-circle' }
      ]
    };
  },
  computed: {
    filteredFaturas() {
      let filtered = [...this.faturas];

      // Filtrar por status (se algum status estiver selecionado)
      if (this.filters.statusList.length > 0) {
        filtered = filtered.filter(fatura => this.filters.statusList.includes(fatura.status));
      }

      if (this.filters.descricao) {
        filtered = filtered.filter(fatura =>
          fatura.descricao.toLowerCase().includes(this.filters.descricao.toLowerCase())
        );
      }

      if (this.filters.data_inicio) {
        filtered = filtered.filter(fatura =>
          new Date(fatura.data_vencimento) >= new Date(this.filters.data_inicio)
        );
      }

      if (this.filters.data_fim) {
        filtered = filtered.filter(fatura =>
          new Date(fatura.data_vencimento) <= new Date(this.filters.data_fim)
        );
      }

      return filtered.sort((a, b) => new Date(b.data_vencimento) - new Date(a.data_vencimento));
    },

    totalPago() {
      return this.faturas
        .filter(f => f.status === 'pago')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalPendente() {
      return this.faturas
        .filter(f => f.status === 'pendente')
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalVencido() {
      return this.faturas
        .filter(f => f.status === 'vencido' || this.isOverdue(f))
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    },

    totalGeral() {
      return this.faturas
        .reduce((sum, f) => sum + parseFloat(f.valor_final || 0), 0);
    }
  },
  methods: {
    formatCurrency: financeiroService.formatCurrency,
    formatDate: financeiroService.formatDate,
    getStatusBadgeClass: financeiroService.getStatusBadgeClass,
    getStatusText: financeiroService.getStatusText,

    applyFilters() {
      // Os filtros são aplicados automaticamente via computed property
    },

    clearFilters() {
      this.filters = {
        statusList: [],
        descricao: '',
        data_inicio: '',
        data_fim: ''
      };
      this.setDefaultDateRange();
    },

    toggleStatus(status) {
      const index = this.filters.statusList.indexOf(status);
      if (index > -1) {
        // Remove o status se já estiver selecionado
        this.filters.statusList.splice(index, 1);
      } else {
        // Adiciona o status se não estiver selecionado
        this.filters.statusList.push(status);
      }
      this.applyFilters();
    },

    getStatusToggleClass(status) {
      const isActive = this.filters.statusList.includes(status);
      const baseClass = 'btn-outline-';
      const activeClass = 'btn-';

      switch (status) {
        case 'pendente':
          return isActive ? activeClass + 'warning' : baseClass + 'warning';
        case 'pago':
          return isActive ? activeClass + 'success' : baseClass + 'success';
        case 'vencido':
          return isActive ? activeClass + 'danger' : baseClass + 'danger';
        case 'cancelado':
          return isActive ? activeClass + 'secondary' : baseClass + 'secondary';
        default:
          return baseClass + 'primary';
      }
    },

    setDefaultDateRange() {
      // Definir data de fim como hoje
      const hoje = new Date();
      this.filters.data_fim = hoje.toISOString().split('T')[0];

      // Definir data de início como 12 meses atrás
      const dozesMesesAtras = new Date();
      dozesMesesAtras.setMonth(dozesMesesAtras.getMonth() - 12);
      this.filters.data_inicio = dozesMesesAtras.toISOString().split('T')[0];
    },

    markAsPaid(fatura) {
      const paymentData = {
        data_pagamento: new Date().toISOString().split('T')[0],
        meio_pagamento: 'Não especificado'
      };
      this.$emit('mark-paid', fatura.id, paymentData);
    },

    isOverdue(fatura) {
      if (fatura.status !== 'pendente') return false;
      return new Date(fatura.data_vencimento) < new Date();
    },

    getDaysOverdue(fatura) {
      if (!this.isOverdue(fatura)) return 0;
      const today = new Date();
      const vencimento = new Date(fatura.data_vencimento);
      const diffTime = today - vencimento;
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    }
  },
  mounted() {
    // Definir o range de datas padrão (últimos 12 meses)
    this.setDefaultDateRange();
  }
};
</script>

<style scoped>
.paciente-financeiro {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.paciente-financeiro .card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.paciente-financeiro .card.flex-fill {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.paciente-financeiro .card.flex-fill .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.paciente-financeiro .table-responsive {
  flex: 1;
}

/* Estilos para os cards de estatísticas */
.stats-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 0.8rem;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.stats-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #344767;
  text-transform: uppercase;
  margin-bottom: 0.4rem;
  letter-spacing: 0.5px;
}

.stats-value {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.badge-warning {
  background-color: #fb6340;
}

.badge-success {
  background-color: #2dce89;
}

.badge-danger {
  background-color: #f5365c;
}

.badge-secondary {
  background-color: #8898aa;
}

.text-info {
  color: #11cdef !important;
}

/* Botões de Ação Elegantes */
.elegant-action-btn {
  min-width: 140px;
  min-height: 80px;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.elegant-action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.elegant-action-btn i {
  transition: all 0.3s ease;
}

.elegant-action-btn:hover i {
  transform: scale(1.1);
}

/* Botões de Ação Pequenos */
.elegant-action-btn-small {
  min-width: 120px;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.elegant-action-btn-small:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Estilos para os toggles de status */
.status-toggles {
  display: flex;
  gap: 0;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-toggle {
  border-radius: 0 !important;
  border-right: none !important;
  font-size: 0.75rem;
  padding: 0.4rem 0.6rem;
  font-weight: 600;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.status-toggle:last-child {
  border-right: 1px solid !important;
}

.status-toggle:first-child {
  border-top-left-radius: 0.375rem !important;
  border-bottom-left-radius: 0.375rem !important;
}

.status-toggle:last-child {
  border-top-right-radius: 0.375rem !important;
  border-bottom-right-radius: 0.375rem !important;
}

.status-toggle:hover {
  transform: translateY(-1px);
  z-index: 2;
  position: relative;
}
</style>
